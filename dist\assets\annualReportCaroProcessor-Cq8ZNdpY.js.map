{"version": 3, "file": "annualReportCaroProcessor-Cq8ZNdpY.js", "sources": ["../../src/lib/annualReportCaroProcessor.ts"], "sourcesContent": ["// annualReportCaroProcessor.ts - Complete Interlinked Annual Report + CARO processor\r\n\r\nimport { CheckResult } from './checkDefinitions';\r\nimport { processSingleDocumentCheck } from './geminiProcessor';\r\nimport {\r\n  checkIncomeeTaxRaidsKeywords,\r\n  checkDefaultsKeywords,\r\n  checkRightsIssueKeywords,\r\n  checkFraudKeywords,\r\n  checkWhistleBlowerKeywords,\r\n  checkCostRecordsKeywords\r\n} from './textSearchProcessor';\r\n\r\nexport interface AnnualReportCaroCheckResult extends CheckResult {\r\n  annualReportFindings?: {\r\n    keywordsFound: string[];\r\n    contexts: Array<{keyword: string, context: string}>;\r\n    totalMatches: number;\r\n  };\r\n  caroClauseAnalysis?: {\r\n    clausePresent: boolean;\r\n    clauseContent: string;\r\n    complianceStatus: string;\r\n    clauseNumber: string;\r\n  };\r\n  crossComplianceStatus?: {\r\n    keywordsFoundInAnnualReport: boolean;\r\n    expectedCaroClause: string;\r\n    caroClausePresent: boolean;\r\n    overallCompliant: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Income Tax Raids Check\r\n * Keywords: \"Income tax raids\", \"Raids by Income tax authorities\", \"Unrecorded income\"\r\n * Expected CARO Clause: (viii)\r\n */\r\nexport async function processAnnualReportCaroIncomeTax(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Income Tax Raids check');\r\n    \r\n    // Step 1: Search Annual Report for income tax keywords\r\n    const annualReportResult = await checkIncomeeTaxRaidsKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      // No keywords found, so CARO clause (viii) is not required\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No income tax raids, unrecorded income keywords found in annual report. CARO clause (viii) not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(viii)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(viii)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    // Step 2: Keywords found, check CARO clause (viii)\r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_viii_check',\r\n      'caro_clause_viii_income_tax',\r\n      parameters\r\n    );\r\n    \r\n    // Step 3: Evaluate compliance\r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Income tax related keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), and CARO clause (viii) is properly present and addresses income tax matters. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Income tax keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), but CARO clause (viii) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions income tax raids or unrecorded income, corresponding CARO clause (viii) must be included.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(viii)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(viii)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Income Tax check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Income Tax cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(viii)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Defaults Check\r\n * Keywords: \"Defaults\", \"Wilful Defaulter\", \"Diversion\"\r\n * Expected CARO Clause: (ix)\r\n */\r\nexport async function processAnnualReportCaroDefaults(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Defaults check');\r\n    \r\n    const annualReportResult = await checkDefaultsKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No defaults, wilful defaulter, or diversion keywords found in annual report. CARO clause (ix) analysis not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(ix)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(ix)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_ix_check',\r\n      'caro_clause_ix_defaults',\r\n      parameters\r\n    );\r\n    \r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Defaults keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), and CARO clause (ix) is properly present and addresses default-related matters. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Defaults keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), but CARO clause (ix) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions defaults or wilful defaulter status, corresponding CARO clause (ix) must be included.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(ix)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(ix)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Defaults check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Defaults cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(ix)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Rights Issue Check\r\n * Keywords: \"Rights issue\"\r\n * Expected CARO Clause: (x)(b)\r\n */\r\nexport async function processAnnualReportCaroRightsIssue(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Rights Issue check');\r\n    \r\n    const annualReportResult = await checkRightsIssueKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No rights issue keywords found in annual report. CARO clause (x)(b) analysis not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(x)(b)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(x)(b)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_x_b_check',\r\n      'caro_clause_x_b_rights_issue',\r\n      parameters\r\n    );\r\n    \r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Rights issue keywords found in annual report, and CARO clause (x)(b) is properly present and addresses rights issue matters. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Rights issue keywords found in annual report, but CARO clause (x)(b) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions rights issue of shares or convertible debentures, corresponding CARO clause (x)(b) must be included.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(x)(b)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(x)(b)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Rights Issue check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Rights Issue cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(x)(b)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Fraud Check\r\n * Keywords: \"fraud\"\r\n * Expected CARO Clause: (xi)(a)\r\n */\r\nexport async function processAnnualReportCaroFraud(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Fraud check');\r\n    \r\n    const annualReportResult = await checkFraudKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No fraud keywords found in annual report. CARO clause (xi)(a) analysis not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(xi)(a)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(xi)(a)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_xi_a_check',\r\n      'caro_clause_xi_a_fraud',\r\n      parameters\r\n    );\r\n    \r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Fraud keywords found in annual report, and CARO clause (xi)(a) is properly present and addresses fraud matters. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Fraud keywords found in annual report, but CARO clause (xi)(a) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions fraud, corresponding CARO clause (xi)(a) must be included.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(xi)(a)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(xi)(a)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Fraud check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Fraud cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(xi)(a)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Whistle-blower Check\r\n * Keywords: \"whistle-blower\", \"whistleblower\"\r\n * Expected CARO Clause: (xi)(c)\r\n */\r\nexport async function processAnnualReportCaroWhistleBlower(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Whistle-blower check');\r\n    \r\n    const annualReportResult = await checkWhistleBlowerKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No whistle-blower keywords found in annual report. CARO clause (xi)(c) analysis not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(xi)(c)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(xi)(c)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_xi_c_check',\r\n      'caro_clause_xi_c_whistleblower',\r\n      parameters\r\n    );\r\n    \r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Whistle-blower keywords found in annual report, and CARO clause (xi)(c) is properly present and addresses whistle-blower complaint handling. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Whistle-blower keywords found in annual report, but CARO clause (xi)(c) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions whistle-blower mechanisms or complaints, corresponding CARO clause (xi)(c) must be included.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(xi)(c)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(xi)(c)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Whistle-blower check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Whistle-blower cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(xi)(c)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Annual Report + CARO Cost Records Check\r\n * Keywords: \"Cost records\", \"Cost Auditor\"\r\n * Expected CARO Clause: (vi)\r\n * Note: Typically applies when turnover from related products/services ≥ ₹35 crores\r\n */\r\nexport async function processAnnualReportCaroCostRecords(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>\r\n): Promise<AnnualReportCaroCheckResult> {\r\n  try {\r\n    console.log('Processing Annual Report + CARO Cost Records check');\r\n    \r\n    const annualReportResult = await checkCostRecordsKeywords(annualReportFile);\r\n    \r\n    if (!annualReportResult.isCompliant) {\r\n      return {\r\n        isCompliant: true,\r\n        explanation: 'No cost records or cost auditor keywords found in annual report. CARO clause (vi) analysis not required for this cross-compliance check.',\r\n        confidence: 0.9,\r\n        annualReportFindings: {\r\n          keywordsFound: [],\r\n          contexts: [],\r\n          totalMatches: 0\r\n        },\r\n        caroClauseAnalysis: {\r\n          clausePresent: false,\r\n          clauseContent: 'Not required - no triggering keywords found',\r\n          complianceStatus: 'Not applicable',\r\n          clauseNumber: '(vi)'\r\n        },\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: '(vi)',\r\n          caroClausePresent: false,\r\n          overallCompliant: true\r\n        }\r\n      };\r\n    }\r\n    \r\n    const caroResult = await processSingleDocumentCheck(\r\n      caroFile,\r\n      'caro_clause_vi_check',\r\n      'caro_clause_vi_cost_records',\r\n      parameters\r\n    );\r\n    \r\n    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;\r\n    \r\n    return {\r\n      isCompliant,\r\n      explanation: isCompliant\r\n        ? `✅ COMPLIANT: Cost records keywords found in annual report, and CARO clause (vi) is properly present and addresses cost records maintenance requirements. Cross-document compliance maintained.`\r\n        : `❌ NON-COMPLIANT: Cost records keywords found in annual report, but CARO clause (vi) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions cost records or cost auditor, corresponding CARO clause (vi) must be included if turnover threshold (₹35 crores) is met.`,\r\n      confidence: 0.9,\r\n      annualReportFindings: {\r\n        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],\r\n        contexts: annualReportResult.extractedData?.contexts || [],\r\n        totalMatches: annualReportResult.extractedData?.totalMatches || 0\r\n      },\r\n      caroClauseAnalysis: {\r\n        clausePresent: caroResult.isCompliant,\r\n        clauseContent: caroResult.explanation,\r\n        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',\r\n        clauseNumber: '(vi)'\r\n      },\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: true,\r\n        expectedCaroClause: '(vi)',\r\n        caroClausePresent: caroResult.isCompliant,\r\n        overallCompliant: isCompliant\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Error processing Annual Report + CARO Cost Records check:', error);\r\n    return {\r\n      isCompliant: false,\r\n      explanation: `Technical error during Annual Report + CARO Cost Records cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,\r\n      confidence: 0.5,\r\n      crossComplianceStatus: {\r\n        keywordsFoundInAnnualReport: false,\r\n        expectedCaroClause: '(vi)',\r\n        caroClausePresent: false,\r\n        overallCompliant: false\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process all Annual Report + CARO checks in batch\r\n */\r\nexport async function processAllAnnualReportCaroChecks(\r\n  annualReportFile: File,\r\n  caroFile: File,\r\n  parameters: Record<string, any>,\r\n  onProgress?: (current: number, total: number, checkName: string) => void\r\n): Promise<Record<string, AnnualReportCaroCheckResult>> {\r\n  \r\n  const checks = [\r\n    { name: 'Income Tax Raids', processor: processAnnualReportCaroIncomeTax, id: 'annual_report_caro_income_tax' },\r\n    { name: 'Defaults/Wilful Defaulter', processor: processAnnualReportCaroDefaults, id: 'annual_report_caro_defaults' },\r\n    { name: 'Rights Issue', processor: processAnnualReportCaroRightsIssue, id: 'annual_report_caro_rights_issue' },\r\n    { name: 'Fraud', processor: processAnnualReportCaroFraud, id: 'annual_report_caro_fraud' },\r\n    { name: 'Whistle-blower', processor: processAnnualReportCaroWhistleBlower, id: 'annual_report_caro_whistleblower' },\r\n    { name: 'Cost Records', processor: processAnnualReportCaroCostRecords, id: 'annual_report_caro_cost_records' }\r\n  ];\r\n  \r\n  const results: Record<string, AnnualReportCaroCheckResult> = {};\r\n  \r\n  console.log(`🚀 Processing ${checks.length} Annual Report + CARO cross-compliance checks`);\r\n  \r\n  for (let i = 0; i < checks.length; i++) {\r\n    const check = checks[i];\r\n    \r\n    if (onProgress) {\r\n      onProgress(i, checks.length, `Annual Report + CARO: ${check.name}`);\r\n    }\r\n    \r\n    console.log(`📋 Processing check ${i + 1}/${checks.length}: ${check.name}`);\r\n    \r\n    try {\r\n      const startTime = Date.now();\r\n      const result = await check.processor(annualReportFile, caroFile, parameters);\r\n      const duration = Date.now() - startTime;\r\n      \r\n      results[check.id] = result;\r\n      \r\n      console.log(`${result.isCompliant ? '✅' : '❌'} ${check.name}: ${result.isCompliant ? 'COMPLIANT' : 'NON-COMPLIANT'} (${duration}ms)`);\r\n      \r\n      if (result.crossComplianceStatus?.keywordsFoundInAnnualReport) {\r\n        console.log(`   📝 Keywords found: ${result.annualReportFindings?.keywordsFound.join(', ')}`);\r\n        console.log(`   📋 Expected CARO clause: ${result.crossComplianceStatus.expectedCaroClause}`);\r\n        console.log(`   ✅ CARO clause present: ${result.crossComplianceStatus.caroClausePresent ? 'Yes' : 'No'}`);\r\n      } else {\r\n        console.log(`   📝 No triggering keywords found in annual report`);\r\n      }\r\n      \r\n      // Small delay between checks\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n      \r\n    } catch (error) {\r\n      console.error(`❌ Error processing ${check.name}:`, error);\r\n      results[check.id] = {\r\n        isCompliant: false,\r\n        explanation: `Error processing ${check.name}: ${error instanceof Error ? error.message : String(error)}`,\r\n        confidence: 0.5,\r\n        crossComplianceStatus: {\r\n          keywordsFoundInAnnualReport: false,\r\n          expectedCaroClause: 'unknown',\r\n          caroClausePresent: false,\r\n          overallCompliant: false\r\n        }\r\n      };\r\n    }\r\n  }\r\n  \r\n  // Final progress update\r\n  if (onProgress) {\r\n    onProgress(checks.length, checks.length, 'Annual Report + CARO checks complete');\r\n  }\r\n  \r\n  console.log(`🎉 Completed ${Object.keys(results).length} Annual Report + CARO cross-compliance checks`);\r\n  \r\n  const compliantCount = Object.values(results).filter(r => r.isCompliant).length;\r\n  console.log(`📊 Results: ${compliantCount}/${Object.keys(results).length} compliant`);\r\n  \r\n  return results;\r\n}\r\n\r\n/**\r\n * Get summary of Annual Report + CARO check results\r\n */\r\nexport function getAnnualReportCaroSummary(results: Record<string, AnnualReportCaroCheckResult>): {\r\n  totalChecks: number;\r\n  compliantChecks: number;\r\n  checksWithKeywords: number;\r\n  checksRequiringCaroClauses: number;\r\n  compliancePercentage: number;\r\n  keywordFindings: Array<{checkId: string, keywords: string[], expectedClause: string}>;\r\n} {\r\n  const totalChecks = Object.keys(results).length;\r\n  const compliantChecks = Object.values(results).filter(r => r.isCompliant).length;\r\n  const checksWithKeywords = Object.values(results).filter(r => \r\n    r.crossComplianceStatus?.keywordsFoundInAnnualReport === true\r\n  ).length;\r\n  const checksRequiringCaroClauses = checksWithKeywords; // Same as checks with keywords\r\n  const compliancePercentage = totalChecks > 0 ? Math.round((compliantChecks / totalChecks) * 100) : 0;\r\n  \r\n  const keywordFindings = Object.entries(results)\r\n    .filter(([_, result]) => result.crossComplianceStatus?.keywordsFoundInAnnualReport === true)\r\n    .map(([checkId, result]) => ({\r\n      checkId,\r\n      keywords: result.annualReportFindings?.keywordsFound || [],\r\n      expectedClause: result.crossComplianceStatus?.expectedCaroClause || 'unknown'\r\n    }));\r\n  \r\n  return {\r\n    totalChecks,\r\n    compliantChecks,\r\n    checksWithKeywords,\r\n    checksRequiringCaroClauses,\r\n    compliancePercentage,\r\n    keywordFindings\r\n  };\r\n}\r\n\r\n/**\r\n * Debug function for Annual Report + CARO checks\r\n */\r\nexport function debugAnnualReportCaroCheck(\r\n  checkId: string,\r\n  annualReportFile: File | undefined,\r\n  caroFile: File | undefined,\r\n  parameters: Record<string, any>\r\n): {\r\n  checkFound: boolean;\r\n  hasRequiredDocuments: boolean;\r\n  missingDocuments: string[];\r\n  conditionsMet: boolean;\r\n  checkDetails: {\r\n    name: string;\r\n    expectedKeywords: string[];\r\n    expectedCaroClause: string;\r\n    applicableReportTypes: string[];\r\n  };\r\n} {\r\n  const checkRegistry = {\r\n    'annual_report_caro_income_tax': {\r\n      name: 'Income Tax Raids Check',\r\n      expectedKeywords: ['Income tax raids', 'Raids by Income tax authorities', 'Unrecorded income'],\r\n      expectedCaroClause: '(viii)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    },\r\n    'annual_report_caro_defaults': {\r\n      name: 'Defaults/Wilful Defaulter Check',\r\n      expectedKeywords: ['Defaults', 'Wilful Defaulter', 'Diversion'],\r\n      expectedCaroClause: '(ix)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    },\r\n    'annual_report_caro_rights_issue': {\r\n      name: 'Rights Issue Check',\r\n      expectedKeywords: ['Rights issue'],\r\n      expectedCaroClause: '(x)(b)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    },\r\n    'annual_report_caro_fraud': {\r\n      name: 'Fraud Check',\r\n      expectedKeywords: ['fraud'],\r\n      expectedCaroClause: '(xi)(a)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    },\r\n    'annual_report_caro_whistleblower': {\r\n      name: 'Whistle-blower Check',\r\n      expectedKeywords: ['whistle-blower', 'whistleblower'],\r\n      expectedCaroClause: '(xi)(c)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    },\r\n    'annual_report_caro_cost_records': {\r\n      name: 'Cost Records Check',\r\n      expectedKeywords: ['Cost records', 'Cost Auditor'],\r\n      expectedCaroClause: '(vi)',\r\n      applicableReportTypes: ['Normal', 'Standalone']\r\n    }\r\n  };\r\n  \r\n  const check = checkRegistry[checkId as keyof typeof checkRegistry];\r\n  \r\n  if (!check) {\r\n    return {\r\n      checkFound: false,\r\n      hasRequiredDocuments: false,\r\n      missingDocuments: ['annual_report', 'annexure_a'],\r\n      conditionsMet: false,\r\n      checkDetails: {\r\n        name: 'Unknown Check',\r\n        expectedKeywords: [],\r\n        expectedCaroClause: 'unknown',\r\n        applicableReportTypes: []\r\n      }\r\n    };\r\n  }\r\n  \r\n  const missingDocuments: string[] = [];\r\n  if (!annualReportFile) missingDocuments.push('annual_report');\r\n  if (!caroFile) missingDocuments.push('annexure_a');\r\n  \r\n  const hasRequiredDocuments = missingDocuments.length === 0;\r\n  \r\n  // Check conditions (audit report type)\r\n  const auditReportType = parameters.audit_report_type;\r\n  const conditionsMet = check.applicableReportTypes.includes(auditReportType);\r\n  \r\n  return {\r\n    checkFound: true,\r\n    hasRequiredDocuments,\r\n    missingDocuments,\r\n    conditionsMet,\r\n    checkDetails: check\r\n  };\r\n}\r\n\r\n/**\r\n * Validate Annual Report + CARO check parameters\r\n */\r\nexport function validateAnnualReportCaroParameters(\r\n  documents: { annual_report?: File; annexure_a?: File },\r\n  parameters: Record<string, any>\r\n): {\r\n  isValid: boolean;\r\n  errors: string[];\r\n  warnings: string[];\r\n  applicableChecks: string[];\r\n} {\r\n  const errors: string[] = [];\r\n  const warnings: string[] = [];\r\n  const applicableChecks: string[] = [];\r\n  \r\n  // Document validation\r\n  if (!documents.annual_report) {\r\n    errors.push('Annual Report is required for Annual Report + CARO cross-compliance checks');\r\n  } else {\r\n    if (documents.annual_report.type !== 'application/pdf') {\r\n      errors.push('Annual Report must be a PDF file');\r\n    }\r\n    if (documents.annual_report.size === 0) {\r\n      errors.push('Annual Report file is empty');\r\n    }\r\n    if (documents.annual_report.size > 50 * 1024 * 1024) {\r\n      warnings.push('Annual Report file is very large and may slow processing');\r\n    }\r\n  }\r\n  \r\n  if (!documents.annexure_a) {\r\n    errors.push('CARO Annexure A is required for Annual Report + CARO cross-compliance checks');\r\n  } else {\r\n    if (documents.annexure_a.type !== 'application/pdf') {\r\n      errors.push('CARO Annexure A must be a PDF file');\r\n    }\r\n    if (documents.annexure_a.size === 0) {\r\n      errors.push('CARO Annexure A file is empty');\r\n    }\r\n  }\r\n  \r\n  // Parameter validation\r\n  const auditReportType = parameters.audit_report_type;\r\n  if (!auditReportType) {\r\n    errors.push('Audit report type is required');\r\n  } else if (!['Normal', 'Standalone', 'Consolidated'].includes(auditReportType)) {\r\n    errors.push(`Invalid audit report type: ${auditReportType}`);\r\n  } else if (auditReportType === 'Consolidated') {\r\n    warnings.push('Annual Report + CARO checks are primarily designed for Normal/Standalone reports');\r\n  } else {\r\n    // Add applicable checks for Normal/Standalone\r\n    applicableChecks.push(\r\n      'annual_report_caro_income_tax',\r\n      'annual_report_caro_defaults',\r\n      'annual_report_caro_rights_issue',\r\n      'annual_report_caro_fraud',\r\n      'annual_report_caro_whistleblower',\r\n      'annual_report_caro_cost_records'\r\n    );\r\n  }\r\n  \r\n  if (!parameters.company_name) {\r\n    warnings.push('Company name not provided - may affect check descriptions');\r\n  }\r\n  \r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    applicableChecks\r\n  };\r\n}\r\n\r\n/**\r\n * Generate Annual Report + CARO check configuration\r\n */\r\nexport function getAnnualReportCaroCheckConfiguration(): {\r\n  totalChecks: number;\r\n  checkDetails: Array<{\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    keywords: string[];\r\n    expectedCaroClause: string;\r\n    method: 'text_search' | 'ai_analysis';\r\n    documentTypes: string[];\r\n  }>;\r\n  processingFlow: string[];\r\n} {\r\n  const checkDetails = [\r\n    {\r\n      id: 'annual_report_caro_income_tax',\r\n      name: 'Income Tax Raids Cross-Compliance',\r\n      description: 'Searches Annual Report for income tax raid keywords and verifies corresponding CARO clause (viii)',\r\n      keywords: ['Income tax raids', 'Raids by Income tax authorities', 'Unrecorded income'],\r\n      expectedCaroClause: '(viii)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    },\r\n    {\r\n      id: 'annual_report_caro_defaults',\r\n      name: 'Defaults/Wilful Defaulter Cross-Compliance',\r\n      description: 'Searches Annual Report for default keywords and verifies corresponding CARO clause (ix)',\r\n      keywords: ['Defaults', 'Wilful Defaulter', 'Diversion'],\r\n      expectedCaroClause: '(ix)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    },\r\n    {\r\n      id: 'annual_report_caro_rights_issue',\r\n      name: 'Rights Issue Cross-Compliance',\r\n      description: 'Searches Annual Report for rights issue keywords and verifies corresponding CARO clause (x)(b)',\r\n      keywords: ['Rights issue'],\r\n      expectedCaroClause: '(x)(b)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    },\r\n    {\r\n      id: 'annual_report_caro_fraud',\r\n      name: 'Fraud Cross-Compliance',\r\n      description: 'Searches Annual Report for fraud keywords and verifies corresponding CARO clause (xi)(a)',\r\n      keywords: ['fraud'],\r\n      expectedCaroClause: '(xi)(a)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    },\r\n    {\r\n      id: 'annual_report_caro_whistleblower',\r\n      name: 'Whistle-blower Cross-Compliance',\r\n      description: 'Searches Annual Report for whistle-blower keywords and verifies corresponding CARO clause (xi)(c)',\r\n      keywords: ['whistle-blower', 'whistleblower'],\r\n      expectedCaroClause: '(xi)(c)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    },\r\n    {\r\n      id: 'annual_report_caro_cost_records',\r\n      name: 'Cost Records Cross-Compliance',\r\n      description: 'Searches Annual Report for cost records keywords and verifies corresponding CARO clause (vi)',\r\n      keywords: ['Cost records', 'Cost Auditor'],\r\n      expectedCaroClause: '(vi)',\r\n      method: 'text_search' as const,\r\n      documentTypes: ['annual_report', 'annexure_a']\r\n    }\r\n  ];\r\n  \r\n  const processingFlow = [\r\n    '1. Extract text from Annual Report PDF',\r\n    '2. Search for specific keywords using Ctrl+F logic',\r\n    '3. If keywords found, analyze CARO Annexure A using Gemini AI',\r\n    '4. Extract and verify expected CARO clause',\r\n    '5. Cross-validate compliance between documents',\r\n    '6. Generate detailed compliance report'\r\n  ];\r\n  \r\n  return {\r\n    totalChecks: checkDetails.length,\r\n    checkDetails,\r\n    processingFlow\r\n  };\r\n}\r\n\r\n/**\r\n * Export main processing functions for use by other modules\r\n */\r\nexport const AnnualReportCaroProcessors = {\r\n  processIncomeTax: processAnnualReportCaroIncomeTax,\r\n  processDefaults: processAnnualReportCaroDefaults,\r\n  processRightsIssue: processAnnualReportCaroRightsIssue,\r\n  processFraud: processAnnualReportCaroFraud,\r\n  processWhistleBlower: processAnnualReportCaroWhistleBlower,\r\n  processCostRecords: processAnnualReportCaroCostRecords,\r\n  processAll: processAllAnnualReportCaroChecks\r\n};\r\n\r\n/**\r\n * Export utility functions\r\n */\r\nexport const AnnualReportCaroUtils = {\r\n  getSummary: getAnnualReportCaroSummary,\r\n  debug: debugAnnualReportCaroCheck,\r\n  validate: validateAnnualReportCaroParameters,\r\n  getConfiguration: getAnnualReportCaroCheckConfiguration\r\n};"], "names": ["processAnnualReportCaroIncomeTax", "annualReportFile", "caroFile", "parameters", "annualReportResult", "checkIncomeeTaxRaidsKeywords", "caroResult", "processSingleDocumentCheck", "isCompliant", "_b", "_a", "_d", "_c", "_e", "_f", "_g", "error", "processAnnualReportCaroDefaults", "checkDefaultsKeywords", "processAnnualReportCaroRightsIssue", "checkRightsIssueKeywords", "processAnnualReportCaroFraud", "checkFraudKeywords", "processAnnualReportCaroWhistleBlower", "checkWhistleBlowerKeywords", "processAnnualReportCaroCostRecords", "checkCostRecordsKeywords", "processAllAnnualReportCaroChecks", "onProgress", "checks", "results", "i", "check", "startTime", "result", "duration", "resolve", "compliantCount", "r"], "mappings": "kFAsCsB,eAAAA,EACpBC,EACAC,EACAC,EACsC,mBAClC,GAAA,CACF,QAAQ,IAAI,wDAAwD,EAG9D,MAAAC,EAAqB,MAAMC,EAA6BJ,CAAgB,EAE1E,GAAA,CAACG,EAAmB,YAEf,MAAA,CACL,YAAa,GACb,YAAa,2IACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,QAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAKJ,MAAME,EAAa,MAAMC,EACvBL,EACA,yBACA,8BACAC,CAAA,EAIIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,qEAAoEC,GAAAC,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAlC,YAAAD,EAAiD,KAAK,KAAK,wHAC/H,iEAAgEE,GAAAC,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,gBAAlC,YAAAD,EAAiD,KAAK,KAAK,gCAAgCL,EAAW,YAAc,yBAA2B,SAAS,0HAC5N,WAAY,GACZ,qBAAsB,CACpB,gBAAeO,EAAAT,EAAmB,gBAAnB,YAAAS,EAAkC,gBAAiB,CAAC,EACnE,WAAUC,EAAAV,EAAmB,gBAAnB,YAAAU,EAAkC,WAAY,CAAC,EACzD,eAAcC,EAAAX,EAAmB,gBAAnB,YAAAW,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeT,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,QAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,0DAA2DA,CAAK,EACvE,CACL,YAAa,GACb,YAAa,kFAAkFA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACrJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAOsB,eAAAC,EACpBhB,EACAC,EACAC,EACsC,mBAClC,GAAA,CACF,QAAQ,IAAI,gDAAgD,EAEtD,MAAAC,EAAqB,MAAMc,EAAsBjB,CAAgB,EAEnE,GAAA,CAACG,EAAmB,YACf,MAAA,CACL,YAAa,GACb,YAAa,uJACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,MAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAIJ,MAAME,EAAa,MAAMC,EACvBL,EACA,uBACA,0BACAC,CAAA,EAGIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,2DAA0DC,GAAAC,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAlC,YAAAD,EAAiD,KAAK,KAAK,2HACrH,+DAA8DE,GAAAC,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,gBAAlC,YAAAD,EAAiD,KAAK,KAAK,8BAA8BL,EAAW,YAAc,yBAA2B,SAAS,sHACxN,WAAY,GACZ,qBAAsB,CACpB,gBAAeO,EAAAT,EAAmB,gBAAnB,YAAAS,EAAkC,gBAAiB,CAAC,EACnE,WAAUC,EAAAV,EAAmB,gBAAnB,YAAAU,EAAkC,WAAY,CAAC,EACzD,eAAcC,EAAAX,EAAmB,gBAAnB,YAAAW,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeT,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,MAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,wDAAyDA,CAAK,EACrE,CACL,YAAa,GACb,YAAa,gFAAgFA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACnJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAOsB,eAAAG,EACpBlB,EACAC,EACAC,EACsC,WAClC,GAAA,CACF,QAAQ,IAAI,oDAAoD,EAE1D,MAAAC,EAAqB,MAAMgB,EAAyBnB,CAAgB,EAEtE,GAAA,CAACG,EAAmB,YACf,MAAA,CACL,YAAa,GACb,YAAa,6HACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,QAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAIJ,MAAME,EAAa,MAAMC,EACvBL,EACA,wBACA,+BACAC,CAAA,EAGIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,kLACA,4FAA4FF,EAAW,YAAc,yBAA2B,SAAS,qIAC7J,WAAY,GACZ,qBAAsB,CACpB,gBAAeI,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAiB,CAAC,EACnE,WAAUD,EAAAL,EAAmB,gBAAnB,YAAAK,EAAkC,WAAY,CAAC,EACzD,eAAcG,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeN,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,QAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,4DAA6DA,CAAK,EACzE,CACL,YAAa,GACb,YAAa,oFAAoFA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACvJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,SACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAOsB,eAAAK,EACpBpB,EACAC,EACAC,EACsC,WAClC,GAAA,CACF,QAAQ,IAAI,6CAA6C,EAEnD,MAAAC,EAAqB,MAAMkB,EAAmBrB,CAAgB,EAEhE,GAAA,CAACG,EAAmB,YACf,MAAA,CACL,YAAa,GACb,YAAa,uHACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,SAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAIJ,MAAME,EAAa,MAAMC,EACvBL,EACA,yBACA,yBACAC,CAAA,EAGIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,qKACA,sFAAsFF,EAAW,YAAc,yBAA2B,SAAS,2FACvJ,WAAY,GACZ,qBAAsB,CACpB,gBAAeI,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAiB,CAAC,EACnE,WAAUD,EAAAL,EAAmB,gBAAnB,YAAAK,EAAkC,WAAY,CAAC,EACzD,eAAcG,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeN,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,SAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,qDAAsDA,CAAK,EAClE,CACL,YAAa,GACb,YAAa,6EAA6EA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GAChJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAOsB,eAAAO,EACpBtB,EACAC,EACAC,EACsC,WAClC,GAAA,CACF,QAAQ,IAAI,sDAAsD,EAE5D,MAAAC,EAAqB,MAAMoB,EAA2BvB,CAAgB,EAExE,GAAA,CAACG,EAAmB,YACf,MAAA,CACL,YAAa,GACb,YAAa,gIACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,SAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAIJ,MAAME,EAAa,MAAMC,EACvBL,EACA,yBACA,iCACAC,CAAA,EAGIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,kMACA,+FAA+FF,EAAW,YAAc,yBAA2B,SAAS,6HAChK,WAAY,GACZ,qBAAsB,CACpB,gBAAeI,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAiB,CAAC,EACnE,WAAUD,EAAAL,EAAmB,gBAAnB,YAAAK,EAAkC,WAAY,CAAC,EACzD,eAAcG,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeN,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,SAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,8DAA+DA,CAAK,EAC3E,CACL,YAAa,GACb,YAAa,sFAAsFA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACzJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAQsB,eAAAS,EACpBxB,EACAC,EACAC,EACsC,WAClC,GAAA,CACF,QAAQ,IAAI,oDAAoD,EAE1D,MAAAC,EAAqB,MAAMsB,EAAyBzB,CAAgB,EAEtE,GAAA,CAACG,EAAmB,YACf,MAAA,CACL,YAAa,GACb,YAAa,2IACb,WAAY,GACZ,qBAAsB,CACpB,cAAe,CAAC,EAChB,SAAU,CAAC,EACX,aAAc,CAChB,EACA,mBAAoB,CAClB,cAAe,GACf,cAAe,8CACf,iBAAkB,iBAClB,aAAc,MAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,EAIJ,MAAME,EAAa,MAAMC,EACvBL,EACA,uBACA,8BACAC,CAAA,EAGIK,EAAcJ,EAAmB,aAAeE,EAAW,YAE1D,MAAA,CACL,YAAAE,EACA,YAAaA,EACT,iMACA,0FAA0FF,EAAW,YAAc,yBAA2B,SAAS,yJAC3J,WAAY,GACZ,qBAAsB,CACpB,gBAAeI,EAAAN,EAAmB,gBAAnB,YAAAM,EAAkC,gBAAiB,CAAC,EACnE,WAAUD,EAAAL,EAAmB,gBAAnB,YAAAK,EAAkC,WAAY,CAAC,EACzD,eAAcG,EAAAR,EAAmB,gBAAnB,YAAAQ,EAAkC,eAAgB,CAClE,EACA,mBAAoB,CAClB,cAAeN,EAAW,YAC1B,cAAeA,EAAW,YAC1B,iBAAkBA,EAAW,YAAc,YAAc,gBACzD,aAAc,MAChB,EACA,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmBA,EAAW,YAC9B,iBAAkBE,CACpB,CAAA,QAGKQ,EAAO,CACN,eAAA,MAAM,4DAA6DA,CAAK,EACzE,CACL,YAAa,GACb,YAAa,oFAAoFA,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACvJ,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,OACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAKA,eAAsBW,EACpB1B,EACAC,EACAC,EACAyB,EACsD,SAEtD,MAAMC,EAAS,CACb,CAAE,KAAM,mBAAoB,UAAW7B,EAAkC,GAAI,+BAAgC,EAC7G,CAAE,KAAM,4BAA6B,UAAWiB,EAAiC,GAAI,6BAA8B,EACnH,CAAE,KAAM,eAAgB,UAAWE,EAAoC,GAAI,iCAAkC,EAC7G,CAAE,KAAM,QAAS,UAAWE,EAA8B,GAAI,0BAA2B,EACzF,CAAE,KAAM,iBAAkB,UAAWE,EAAsC,GAAI,kCAAmC,EAClH,CAAE,KAAM,eAAgB,UAAWE,EAAoC,GAAI,iCAAkC,CAAA,EAGzGK,EAAuD,CAAA,EAE7D,QAAQ,IAAI,iBAAiBD,EAAO,MAAM,+CAA+C,EAEzF,QAASE,EAAI,EAAGA,EAAIF,EAAO,OAAQE,IAAK,CAChC,MAAAC,EAAQH,EAAOE,CAAC,EAElBH,GACFA,EAAWG,EAAGF,EAAO,OAAQ,yBAAyBG,EAAM,IAAI,EAAE,EAG5D,QAAA,IAAI,uBAAuBD,EAAI,CAAC,IAAIF,EAAO,MAAM,KAAKG,EAAM,IAAI,EAAE,EAEtE,GAAA,CACI,MAAAC,EAAY,KAAK,MACjBC,EAAS,MAAMF,EAAM,UAAU/B,EAAkBC,EAAUC,CAAU,EACrEgC,EAAW,KAAK,IAAA,EAAQF,EAEtBH,EAAAE,EAAM,EAAE,EAAIE,EAEpB,QAAQ,IAAI,GAAGA,EAAO,YAAc,IAAM,GAAG,IAAIF,EAAM,IAAI,KAAKE,EAAO,YAAc,YAAc,eAAe,KAAKC,CAAQ,KAAK,GAEhIzB,EAAAwB,EAAO,wBAAP,MAAAxB,EAA8B,6BACxB,QAAA,IAAI,0BAAyBD,EAAAyB,EAAO,uBAAP,YAAAzB,EAA6B,cAAc,KAAK,KAAK,EAAE,EAC5F,QAAQ,IAAI,+BAA+ByB,EAAO,sBAAsB,kBAAkB,EAAE,EAC5F,QAAQ,IAAI,6BAA6BA,EAAO,sBAAsB,kBAAoB,MAAQ,IAAI,EAAE,GAExG,QAAQ,IAAI,qDAAqD,EAInE,MAAM,IAAI,QAAQE,GAAW,WAAWA,EAAS,GAAG,CAAC,QAE9CpB,EAAO,CACd,QAAQ,MAAM,sBAAsBgB,EAAM,IAAI,IAAKhB,CAAK,EAChDc,EAAAE,EAAM,EAAE,EAAI,CAClB,YAAa,GACb,YAAa,oBAAoBA,EAAM,IAAI,KAAKhB,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,CAAC,GACtG,WAAY,GACZ,sBAAuB,CACrB,4BAA6B,GAC7B,mBAAoB,UACpB,kBAAmB,GACnB,iBAAkB,EACpB,CAAA,CAEJ,CACF,CAGIY,GACFA,EAAWC,EAAO,OAAQA,EAAO,OAAQ,sCAAsC,EAGjF,QAAQ,IAAI,gBAAgB,OAAO,KAAKC,CAAO,EAAE,MAAM,+CAA+C,EAEhG,MAAAO,EAAiB,OAAO,OAAOP,CAAO,EAAE,OAAOQ,GAAKA,EAAE,WAAW,EAAE,OACjE,eAAA,IAAI,eAAeD,CAAc,IAAI,OAAO,KAAKP,CAAO,EAAE,MAAM,YAAY,EAE7EA,CACT"}
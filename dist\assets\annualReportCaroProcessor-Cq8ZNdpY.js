import{c as R,p as C,a as x,b as h,d as A,e as g,f as y}from"./index-8dRfUuOe.js";async function w(i,u,p){var r,o,s,e,a,t,d;try{console.log("Processing Annual Report + CARO Income Tax Raids check");const n=await R(i);if(!n.isCompliant)return{isCompliant:!0,explanation:"No income tax raids, unrecorded income keywords found in annual report. CARO clause (viii) not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(viii)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(viii)",caroClausePresent:!1,overallCompliant:!0}};const l=await C(u,"caro_clause_viii_check","caro_clause_viii_income_tax",p),c=n.isCompliant&&l.isCompliant;return{isCompliant:c,explanation:c?`✅ COMPLIANT: Income tax related keywords found in annual report (${(o=(r=n.extractedData)==null?void 0:r.foundKeywords)==null?void 0:o.join(", ")}), and CARO clause (viii) is properly present and addresses income tax matters. Cross-document compliance maintained.`:`❌ NON-COMPLIANT: Income tax keywords found in annual report (${(e=(s=n.extractedData)==null?void 0:s.foundKeywords)==null?void 0:e.join(", ")}), but CARO clause (viii) is ${l.isCompliant?"present but inadequate":"missing"}. When annual report mentions income tax raids or unrecorded income, corresponding CARO clause (viii) must be included.`,confidence:.9,annualReportFindings:{keywordsFound:((a=n.extractedData)==null?void 0:a.foundKeywords)||[],contexts:((t=n.extractedData)==null?void 0:t.contexts)||[],totalMatches:((d=n.extractedData)==null?void 0:d.totalMatches)||0},caroClauseAnalysis:{clausePresent:l.isCompliant,clauseContent:l.explanation,complianceStatus:l.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(viii)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(viii)",caroClausePresent:l.isCompliant,overallCompliant:c}}}catch(n){return console.error("Error processing Annual Report + CARO Income Tax check:",n),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Income Tax cross-compliance check: ${n instanceof Error?n.message:String(n)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(viii)",caroClausePresent:!1,overallCompliant:!1}}}}async function k(i,u,p){var r,o,s,e,a,t,d;try{console.log("Processing Annual Report + CARO Defaults check");const n=await x(i);if(!n.isCompliant)return{isCompliant:!0,explanation:"No defaults, wilful defaulter, or diversion keywords found in annual report. CARO clause (ix) analysis not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(ix)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(ix)",caroClausePresent:!1,overallCompliant:!0}};const l=await C(u,"caro_clause_ix_check","caro_clause_ix_defaults",p),c=n.isCompliant&&l.isCompliant;return{isCompliant:c,explanation:c?`✅ COMPLIANT: Defaults keywords found in annual report (${(o=(r=n.extractedData)==null?void 0:r.foundKeywords)==null?void 0:o.join(", ")}), and CARO clause (ix) is properly present and addresses default-related matters. Cross-document compliance maintained.`:`❌ NON-COMPLIANT: Defaults keywords found in annual report (${(e=(s=n.extractedData)==null?void 0:s.foundKeywords)==null?void 0:e.join(", ")}), but CARO clause (ix) is ${l.isCompliant?"present but inadequate":"missing"}. When annual report mentions defaults or wilful defaulter status, corresponding CARO clause (ix) must be included.`,confidence:.9,annualReportFindings:{keywordsFound:((a=n.extractedData)==null?void 0:a.foundKeywords)||[],contexts:((t=n.extractedData)==null?void 0:t.contexts)||[],totalMatches:((d=n.extractedData)==null?void 0:d.totalMatches)||0},caroClauseAnalysis:{clausePresent:l.isCompliant,clauseContent:l.explanation,complianceStatus:l.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(ix)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(ix)",caroClausePresent:l.isCompliant,overallCompliant:c}}}catch(n){return console.error("Error processing Annual Report + CARO Defaults check:",n),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Defaults cross-compliance check: ${n instanceof Error?n.message:String(n)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(ix)",caroClausePresent:!1,overallCompliant:!1}}}}async function O(i,u,p){var r,o,s;try{console.log("Processing Annual Report + CARO Rights Issue check");const e=await h(i);if(!e.isCompliant)return{isCompliant:!0,explanation:"No rights issue keywords found in annual report. CARO clause (x)(b) analysis not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(x)(b)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(x)(b)",caroClausePresent:!1,overallCompliant:!0}};const a=await C(u,"caro_clause_x_b_check","caro_clause_x_b_rights_issue",p),t=e.isCompliant&&a.isCompliant;return{isCompliant:t,explanation:t?"✅ COMPLIANT: Rights issue keywords found in annual report, and CARO clause (x)(b) is properly present and addresses rights issue matters. Cross-document compliance maintained.":`❌ NON-COMPLIANT: Rights issue keywords found in annual report, but CARO clause (x)(b) is ${a.isCompliant?"present but inadequate":"missing"}. When annual report mentions rights issue of shares or convertible debentures, corresponding CARO clause (x)(b) must be included.`,confidence:.9,annualReportFindings:{keywordsFound:((r=e.extractedData)==null?void 0:r.foundKeywords)||[],contexts:((o=e.extractedData)==null?void 0:o.contexts)||[],totalMatches:((s=e.extractedData)==null?void 0:s.totalMatches)||0},caroClauseAnalysis:{clausePresent:a.isCompliant,clauseContent:a.explanation,complianceStatus:a.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(x)(b)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(x)(b)",caroClausePresent:a.isCompliant,overallCompliant:t}}}catch(e){return console.error("Error processing Annual Report + CARO Rights Issue check:",e),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Rights Issue cross-compliance check: ${e instanceof Error?e.message:String(e)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(x)(b)",caroClausePresent:!1,overallCompliant:!1}}}}async function N(i,u,p){var r,o,s;try{console.log("Processing Annual Report + CARO Fraud check");const e=await A(i);if(!e.isCompliant)return{isCompliant:!0,explanation:"No fraud keywords found in annual report. CARO clause (xi)(a) analysis not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(xi)(a)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(xi)(a)",caroClausePresent:!1,overallCompliant:!0}};const a=await C(u,"caro_clause_xi_a_check","caro_clause_xi_a_fraud",p),t=e.isCompliant&&a.isCompliant;return{isCompliant:t,explanation:t?"✅ COMPLIANT: Fraud keywords found in annual report, and CARO clause (xi)(a) is properly present and addresses fraud matters. Cross-document compliance maintained.":`❌ NON-COMPLIANT: Fraud keywords found in annual report, but CARO clause (xi)(a) is ${a.isCompliant?"present but inadequate":"missing"}. When annual report mentions fraud, corresponding CARO clause (xi)(a) must be included.`,confidence:.9,annualReportFindings:{keywordsFound:((r=e.extractedData)==null?void 0:r.foundKeywords)||[],contexts:((o=e.extractedData)==null?void 0:o.contexts)||[],totalMatches:((s=e.extractedData)==null?void 0:s.totalMatches)||0},caroClauseAnalysis:{clausePresent:a.isCompliant,clauseContent:a.explanation,complianceStatus:a.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(xi)(a)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(xi)(a)",caroClausePresent:a.isCompliant,overallCompliant:t}}}catch(e){return console.error("Error processing Annual Report + CARO Fraud check:",e),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Fraud cross-compliance check: ${e instanceof Error?e.message:String(e)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(xi)(a)",caroClausePresent:!1,overallCompliant:!1}}}}async function _(i,u,p){var r,o,s;try{console.log("Processing Annual Report + CARO Whistle-blower check");const e=await g(i);if(!e.isCompliant)return{isCompliant:!0,explanation:"No whistle-blower keywords found in annual report. CARO clause (xi)(c) analysis not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(xi)(c)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(xi)(c)",caroClausePresent:!1,overallCompliant:!0}};const a=await C(u,"caro_clause_xi_c_check","caro_clause_xi_c_whistleblower",p),t=e.isCompliant&&a.isCompliant;return{isCompliant:t,explanation:t?"✅ COMPLIANT: Whistle-blower keywords found in annual report, and CARO clause (xi)(c) is properly present and addresses whistle-blower complaint handling. Cross-document compliance maintained.":`❌ NON-COMPLIANT: Whistle-blower keywords found in annual report, but CARO clause (xi)(c) is ${a.isCompliant?"present but inadequate":"missing"}. When annual report mentions whistle-blower mechanisms or complaints, corresponding CARO clause (xi)(c) must be included.`,confidence:.9,annualReportFindings:{keywordsFound:((r=e.extractedData)==null?void 0:r.foundKeywords)||[],contexts:((o=e.extractedData)==null?void 0:o.contexts)||[],totalMatches:((s=e.extractedData)==null?void 0:s.totalMatches)||0},caroClauseAnalysis:{clausePresent:a.isCompliant,clauseContent:a.explanation,complianceStatus:a.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(xi)(c)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(xi)(c)",caroClausePresent:a.isCompliant,overallCompliant:t}}}catch(e){return console.error("Error processing Annual Report + CARO Whistle-blower check:",e),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Whistle-blower cross-compliance check: ${e instanceof Error?e.message:String(e)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(xi)(c)",caroClausePresent:!1,overallCompliant:!1}}}}async function b(i,u,p){var r,o,s;try{console.log("Processing Annual Report + CARO Cost Records check");const e=await y(i);if(!e.isCompliant)return{isCompliant:!0,explanation:"No cost records or cost auditor keywords found in annual report. CARO clause (vi) analysis not required for this cross-compliance check.",confidence:.9,annualReportFindings:{keywordsFound:[],contexts:[],totalMatches:0},caroClauseAnalysis:{clausePresent:!1,clauseContent:"Not required - no triggering keywords found",complianceStatus:"Not applicable",clauseNumber:"(vi)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(vi)",caroClausePresent:!1,overallCompliant:!0}};const a=await C(u,"caro_clause_vi_check","caro_clause_vi_cost_records",p),t=e.isCompliant&&a.isCompliant;return{isCompliant:t,explanation:t?"✅ COMPLIANT: Cost records keywords found in annual report, and CARO clause (vi) is properly present and addresses cost records maintenance requirements. Cross-document compliance maintained.":`❌ NON-COMPLIANT: Cost records keywords found in annual report, but CARO clause (vi) is ${a.isCompliant?"present but inadequate":"missing"}. When annual report mentions cost records or cost auditor, corresponding CARO clause (vi) must be included if turnover threshold (₹35 crores) is met.`,confidence:.9,annualReportFindings:{keywordsFound:((r=e.extractedData)==null?void 0:r.foundKeywords)||[],contexts:((o=e.extractedData)==null?void 0:o.contexts)||[],totalMatches:((s=e.extractedData)==null?void 0:s.totalMatches)||0},caroClauseAnalysis:{clausePresent:a.isCompliant,clauseContent:a.explanation,complianceStatus:a.isCompliant?"Compliant":"Non-compliant",clauseNumber:"(vi)"},crossComplianceStatus:{keywordsFoundInAnnualReport:!0,expectedCaroClause:"(vi)",caroClausePresent:a.isCompliant,overallCompliant:t}}}catch(e){return console.error("Error processing Annual Report + CARO Cost Records check:",e),{isCompliant:!1,explanation:`Technical error during Annual Report + CARO Cost Records cross-compliance check: ${e instanceof Error?e.message:String(e)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"(vi)",caroClausePresent:!1,overallCompliant:!1}}}}async function P(i,u,p,r){var a,t;const o=[{name:"Income Tax Raids",processor:w,id:"annual_report_caro_income_tax"},{name:"Defaults/Wilful Defaulter",processor:k,id:"annual_report_caro_defaults"},{name:"Rights Issue",processor:O,id:"annual_report_caro_rights_issue"},{name:"Fraud",processor:N,id:"annual_report_caro_fraud"},{name:"Whistle-blower",processor:_,id:"annual_report_caro_whistleblower"},{name:"Cost Records",processor:b,id:"annual_report_caro_cost_records"}],s={};console.log(`🚀 Processing ${o.length} Annual Report + CARO cross-compliance checks`);for(let d=0;d<o.length;d++){const n=o[d];r&&r(d,o.length,`Annual Report + CARO: ${n.name}`),console.log(`📋 Processing check ${d+1}/${o.length}: ${n.name}`);try{const l=Date.now(),c=await n.processor(i,u,p),m=Date.now()-l;s[n.id]=c,console.log(`${c.isCompliant?"✅":"❌"} ${n.name}: ${c.isCompliant?"COMPLIANT":"NON-COMPLIANT"} (${m}ms)`),(a=c.crossComplianceStatus)!=null&&a.keywordsFoundInAnnualReport?(console.log(`   📝 Keywords found: ${(t=c.annualReportFindings)==null?void 0:t.keywordsFound.join(", ")}`),console.log(`   📋 Expected CARO clause: ${c.crossComplianceStatus.expectedCaroClause}`),console.log(`   ✅ CARO clause present: ${c.crossComplianceStatus.caroClausePresent?"Yes":"No"}`)):console.log("   📝 No triggering keywords found in annual report"),await new Promise(f=>setTimeout(f,300))}catch(l){console.error(`❌ Error processing ${n.name}:`,l),s[n.id]={isCompliant:!1,explanation:`Error processing ${n.name}: ${l instanceof Error?l.message:String(l)}`,confidence:.5,crossComplianceStatus:{keywordsFoundInAnnualReport:!1,expectedCaroClause:"unknown",caroClausePresent:!1,overallCompliant:!1}}}}r&&r(o.length,o.length,"Annual Report + CARO checks complete"),console.log(`🎉 Completed ${Object.keys(s).length} Annual Report + CARO cross-compliance checks`);const e=Object.values(s).filter(d=>d.isCompliant).length;return console.log(`📊 Results: ${e}/${Object.keys(s).length} compliant`),s}export{P as processAllAnnualReportCaroChecks,b as processAnnualReportCaroCostRecords,k as processAnnualReportCaroDefaults,N as processAnnualReportCaroFraud,w as processAnnualReportCaroIncomeTax,O as processAnnualReportCaroRightsIssue,_ as processAnnualReportCaroWhistleBlower};
//# sourceMappingURL=annualReportCaroProcessor-Cq8ZNdpY.js.map
